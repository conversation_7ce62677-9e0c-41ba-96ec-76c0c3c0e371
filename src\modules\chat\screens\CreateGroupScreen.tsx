import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { ColorThemes } from '../../../assets/skin/colors';
import { createGroupRoom } from '../../../redux/reducers/ChatReducer';
import { useChatLoading } from '../../../redux/hook/chatHook';
import ChatAPI from '../services/ChatAPI';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import FastImage from 'react-native-fast-image';
import { navigate, RootScreen } from '../../../router/router';
import { InforHeader } from '../../../Screen/Layout/headers/inforHeader';
import ConfigAPI from '../../../Config/ConfigAPI';

interface User {
  id: string;
  Name: string;
  Avatar?: string;
  Email?: string;
}

const CreateGroupScreen: React.FC = () => {
  const dispatch = useDispatch<any>();
  const navigation = useNavigation();
  const loading = useChatLoading();
  
  const [groupName, setGroupName] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      searchUsers();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  const searchUsers = async () => {
    try {
      setIsSearching(true);
      const results = await ChatAPI.searchUsers(searchQuery);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching users:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tìm kiếm người dùng',
      });
    } finally {
      setIsSearching(false);
    }
  };

  const toggleUserSelection = (user: User) => {
    const isSelected = selectedUsers.find(u => u.id === user.id);
    
    if (isSelected) {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
    } else {
      setSelectedUsers([...selectedUsers, user]);
    }
  };

  const createGroup = async () => {
    if (!groupName.trim()) {
      showSnackbar({
        status: ComponentStatus.WARNING,
        message: 'Vui lòng nhập tên nhóm',
      });
      return;
    }

    if (selectedUsers.length < 2) {
      showSnackbar({
        status: ComponentStatus.WARNING,
        message: 'Vui lòng chọn ít nhất 2 thành viên',
      });
      return;
    }

    try {
      const groupData = {
        name: groupName.trim(),
        participants: selectedUsers.map(user => user.id),
      };

      const newRoom = await dispatch(createGroupRoom(groupData));
      
      showSnackbar({
        status: ComponentStatus.SUCCSESS,
        message: 'Tạo nhóm thành công',
      });

      // Navigate to the new chat room
      navigate(RootScreen.ChatRoom, { room: newRoom });
      
    } catch (error) {
      console.error('Error creating group:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tạo nhóm chat',
      });
    }
  };

  const renderSelectedUser = ({ item }: { item: User }) => (
    <View style={styles.selectedUserItem}>
      {item.Avatar ? (
        <FastImage source={{ uri: ConfigAPI.urlImg + item.Avatar }} style={styles.selectedUserAvatar} />
      ) : (
        <View style={[styles.selectedUserAvatar, styles.defaultAvatar]}>
          <Text style={styles.avatarText}>
            {item.Name?.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}
      <Text style={styles.selectedUserName} numberOfLines={1}>
        {item.Name}
      </Text>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => toggleUserSelection(item)}
      >
        <Text style={styles.removeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSearchResult = ({ item }: { item: User }) => {
    const isSelected = selectedUsers.find(u => u.id === item.id);
    
    return (
      <TouchableOpacity
        style={[styles.userItem, isSelected && styles.selectedUserItem]}
        onPress={() => toggleUserSelection(item)}
        activeOpacity={0.7}
      >
        {item.avatar ? (
          <FastImage source={{ uri: item.avatar }} style={styles.userAvatar} />
        ) : (
          <View style={[styles.userAvatar, styles.defaultAvatar]}>
            <Text style={styles.avatarText}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.name}</Text>
          {item.email && (
            <Text style={styles.userEmail}>{item.email}</Text>
          )}
        </View>
        
        <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
          {isSelected && <Text style={styles.checkmark}>✓</Text>}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={'Tạo nhóm chát'}
      customActions={
        <TouchableOpacity
          onPress={createGroup}
          disabled={loading || !groupName.trim() || selectedUsers.length < 2}
        >
          <Text style={[
            styles.createButton,
            (loading || !groupName.trim() || selectedUsers.length < 2) && styles.disabledButton,
            {
              marginRight: 16,
            }
          ]}>
            Tạo
          </Text>
        </TouchableOpacity>

      }
      showAction={true}
      />

      {/* Group Name Input */}
      <View style={styles.groupNameSection}>
        <Text style={styles.sectionTitle}>Tên nhóm</Text>
        <TextInput
          style={styles.groupNameInput}
          value={groupName}
          onChangeText={setGroupName}
          placeholder="Nhập tên nhóm..."
          maxLength={50}
        />
      </View>

      {/* Selected Users */}
      {selectedUsers.length > 0 && (
        <View style={styles.selectedUsersSection}>
          <Text style={styles.sectionTitle}>
            Đã chọn ({selectedUsers.length})
          </Text>
          <FlatList
            data={selectedUsers}
            renderItem={renderSelectedUser}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.selectedUsersList}
          />
        </View>
      )}

      {/* Search Input */}
      <View style={styles.searchSection}>
        <Text style={styles.sectionTitle}>Thêm thành viên</Text>
        <TextInput
          style={styles.searchInput}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Tìm kiếm người dùng..."
          autoCapitalize="none"
        />
      </View>

      {/* Search Results */}
      <FlatList
        data={searchResults}
        renderItem={renderSearchResult}
        keyExtractor={(item) => item.id}
        style={styles.searchResults}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          searchQuery.length > 2 && !isSearching ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Không tìm thấy người dùng</Text>
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  cancelButton: {
    color: ColorThemes.light.error_color,
    fontSize: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_color,
  },
  createButton: {
    color: ColorThemes.light.primary_color,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  groupNameSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
    marginBottom: 8,
  },
  groupNameInput: {
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  selectedUsersSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  selectedUsersList: {
    marginTop: 8,
  },
  selectedUserItem: {
    alignItems: 'center',
    marginRight: 12,
    width: 60,
  },
  selectedUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  selectedUserName: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_color,
    textAlign: 'center',
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.error_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  searchSection: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  searchResults: {
    flex: 1,
    backgroundColor: 'white',
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_color,
  },
  userEmail: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: ColorThemes.light.neutral_border_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedBox: {
    backgroundColor: ColorThemes.light.primary_color,
    borderColor: ColorThemes.light.primary_color,
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
});

export default CreateGroupScreen;
